export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string | null
          description: string
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          id: string
          metadata: Json | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string | null
          description: string
          entity_id: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          id?: string
          metadata?: Json | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string | null
          description?: string
          entity_id?: string
          entity_type?: Database["public"]["Enums"]["entity_type"]
          id?: string
          metadata?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_activities_user_id"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      administrative_divisions: {
        Row: {
          created_at: string | null
          district: string
          id: string
          parish: string | null
          sub_county: string
          village: string | null
        }
        Insert: {
          created_at?: string | null
          district: string
          id?: string
          parish?: string | null
          sub_county: string
          village?: string | null
        }
        Update: {
          created_at?: string | null
          district?: string
          id?: string
          parish?: string | null
          sub_county?: string
          village?: string | null
        }
        Relationships: []
      }
      attendance_analytics: {
        Row: {
          analysis_period: string
          attendance_rate: number
          average_participation_score: number | null
          consecutive_absences: number | null
          created_at: string | null
          created_by: string
          id: string
          improvement_trend: string | null
          intervention_recommended: boolean | null
          last_calculated: string | null
          longest_absence_streak: number | null
          period_end_date: string
          period_start_date: string
          punctuality_rate: number | null
          risk_level: string | null
          school_id: string
          sessions_absent: number
          sessions_attended: number
          sessions_excused: number
          sessions_late: number
          student_id: string | null
          total_sessions: number
        }
        Insert: {
          analysis_period: string
          attendance_rate: number
          average_participation_score?: number | null
          consecutive_absences?: number | null
          created_at?: string | null
          created_by: string
          id?: string
          improvement_trend?: string | null
          intervention_recommended?: boolean | null
          last_calculated?: string | null
          longest_absence_streak?: number | null
          period_end_date: string
          period_start_date: string
          punctuality_rate?: number | null
          risk_level?: string | null
          school_id: string
          sessions_absent: number
          sessions_attended: number
          sessions_excused: number
          sessions_late: number
          student_id?: string | null
          total_sessions: number
        }
        Update: {
          analysis_period?: string
          attendance_rate?: number
          average_participation_score?: number | null
          consecutive_absences?: number | null
          created_at?: string | null
          created_by?: string
          id?: string
          improvement_trend?: string | null
          intervention_recommended?: boolean | null
          last_calculated?: string | null
          longest_absence_streak?: number | null
          period_end_date?: string
          period_start_date?: string
          punctuality_rate?: number | null
          risk_level?: string | null
          school_id?: string
          sessions_absent?: number
          sessions_attended?: number
          sessions_excused?: number
          sessions_late?: number
          student_id?: string | null
          total_sessions?: number
        }
        Relationships: [
          {
            foreignKeyName: "attendance_analytics_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_analytics_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_analytics_school_id"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_analytics_student_id"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_notifications: {
        Row: {
          auto_generated: boolean | null
          created_at: string | null
          created_by: string
          delivery_method: string | null
          delivery_status: string | null
          id: string
          message_content: string
          message_title: string
          notification_data: Json | null
          notification_type: string
          priority_level: string | null
          read_at: string | null
          recipient_contact: string | null
          recipient_type: string
          response_content: string | null
          response_received: boolean | null
          school_id: string
          sent_at: string | null
          session_id: string | null
          student_id: string | null
        }
        Insert: {
          auto_generated?: boolean | null
          created_at?: string | null
          created_by: string
          delivery_method?: string | null
          delivery_status?: string | null
          id?: string
          message_content: string
          message_title: string
          notification_data?: Json | null
          notification_type: string
          priority_level?: string | null
          read_at?: string | null
          recipient_contact?: string | null
          recipient_type: string
          response_content?: string | null
          response_received?: boolean | null
          school_id: string
          sent_at?: string | null
          session_id?: string | null
          student_id?: string | null
        }
        Update: {
          auto_generated?: boolean | null
          created_at?: string | null
          created_by?: string
          delivery_method?: string | null
          delivery_status?: string | null
          id?: string
          message_content?: string
          message_title?: string
          notification_data?: Json | null
          notification_type?: string
          priority_level?: string | null
          read_at?: string | null
          recipient_contact?: string | null
          recipient_type?: string
          response_content?: string | null
          response_received?: boolean | null
          school_id?: string
          sent_at?: string | null
          session_id?: string | null
          student_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_notifications_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_notifications_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_notifications_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "attendance_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_notifications_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_notifications_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_notifications_school_id"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_notifications_session_id"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "attendance_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_attendance_notifications_student_id"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_sessions: {
        Row: {
          actual_duration_minutes: number | null
          created_at: string | null
          created_by: string
          end_time: string | null
          facilitator_id: string | null
          grade_level: number | null
          id: string
          is_completed: boolean | null
          learning_objectives: string[] | null
          location: string | null
          materials_needed: string[] | null
          max_capacity: number | null
          planned_duration_minutes: number | null
          round_tables_count: number | null
          school_id: string
          session_date: string
          session_description: string | null
          session_name: string
          session_type: Database["public"]["Enums"]["session_type"]
          special_notes: string | null
          start_time: string
          students_per_table: number | null
          subject: string | null
          teacher_name: string | null
          updated_at: string | null
          weather_condition: string | null
        }
        Insert: {
          actual_duration_minutes?: number | null
          created_at?: string | null
          created_by: string
          end_time?: string | null
          facilitator_id?: string | null
          grade_level?: number | null
          id?: string
          is_completed?: boolean | null
          learning_objectives?: string[] | null
          location?: string | null
          materials_needed?: string[] | null
          max_capacity?: number | null
          planned_duration_minutes?: number | null
          round_tables_count?: number | null
          school_id: string
          session_date: string
          session_description?: string | null
          session_name: string
          session_type: Database["public"]["Enums"]["session_type"]
          special_notes?: string | null
          start_time: string
          students_per_table?: number | null
          subject?: string | null
          teacher_name?: string | null
          updated_at?: string | null
          weather_condition?: string | null
        }
        Update: {
          actual_duration_minutes?: number | null
          created_at?: string | null
          created_by?: string
          end_time?: string | null
          facilitator_id?: string | null
          grade_level?: number | null
          id?: string
          is_completed?: boolean | null
          learning_objectives?: string[] | null
          location?: string | null
          materials_needed?: string[] | null
          max_capacity?: number | null
          planned_duration_minutes?: number | null
          round_tables_count?: number | null
          school_id?: string
          session_date?: string
          session_description?: string | null
          session_name?: string
          session_type?: Database["public"]["Enums"]["session_type"]
          special_notes?: string | null
          start_time?: string
          students_per_table?: number | null
          subject?: string | null
          teacher_name?: string | null
          updated_at?: string | null
          weather_condition?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_sessions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_facilitator_id_fkey"
            columns: ["facilitator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: Database["public"]["Enums"]["audit_action"]
          created_at: string | null
          details: Json | null
          id: string
          ip_address: unknown | null
          performed_by: string
          performed_by_name: string
          target_user_email: string | null
          target_user_id: string | null
          user_agent: string | null
        }
        Insert: {
          action: Database["public"]["Enums"]["audit_action"]
          created_at?: string | null
          details?: Json | null
          id?: string
          ip_address?: unknown | null
          performed_by: string
          performed_by_name: string
          target_user_email?: string | null
          target_user_id?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: Database["public"]["Enums"]["audit_action"]
          created_at?: string | null
          details?: Json | null
          id?: string
          ip_address?: unknown | null
          performed_by?: string
          performed_by_name?: string
          target_user_email?: string | null
          target_user_id?: string | null
          user_agent?: string | null
        }
        Relationships: []
      }
      book_distributions: {
        Row: {
          actual_delivery_date: string | null
          book_id: string
          created_at: string | null
          created_by: string
          delivery_confirmation: boolean | null
          id: string
          inventory_id: string
          notes: string | null
          planned_date: string | null
          quantity: number
          recipient_name: string | null
          recipient_title: string | null
          school_id: string
          status: Database["public"]["Enums"]["distribution_status"]
          supervisor_id: string
          updated_at: string | null
        }
        Insert: {
          actual_delivery_date?: string | null
          book_id: string
          created_at?: string | null
          created_by: string
          delivery_confirmation?: boolean | null
          id?: string
          inventory_id: string
          notes?: string | null
          planned_date?: string | null
          quantity: number
          recipient_name?: string | null
          recipient_title?: string | null
          school_id: string
          status?: Database["public"]["Enums"]["distribution_status"]
          supervisor_id: string
          updated_at?: string | null
        }
        Update: {
          actual_delivery_date?: string | null
          book_id?: string
          created_at?: string | null
          created_by?: string
          delivery_confirmation?: boolean | null
          id?: string
          inventory_id?: string
          notes?: string | null
          planned_date?: string | null
          quantity?: number
          recipient_name?: string | null
          recipient_title?: string | null
          school_id?: string
          status?: Database["public"]["Enums"]["distribution_status"]
          supervisor_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "book_distributions_book_id_fkey"
            columns: ["book_id"]
            isOneToOne: false
            referencedRelation: "books"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_distributions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_distributions_inventory_id_fkey"
            columns: ["inventory_id"]
            isOneToOne: false
            referencedRelation: "book_inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_distributions_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_distributions_supervisor_id_fkey"
            columns: ["supervisor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      book_inventory: {
        Row: {
          available_quantity: number
          book_id: string
          condition: Database["public"]["Enums"]["book_condition"]
          cost_per_unit: number | null
          created_at: string | null
          damaged_quantity: number
          distributed_quantity: number
          id: string
          last_updated_by: string
          lost_quantity: number
          minimum_threshold: number
          notes: string | null
          storage_location: string | null
          total_quantity: number
          updated_at: string | null
        }
        Insert: {
          available_quantity?: number
          book_id: string
          condition?: Database["public"]["Enums"]["book_condition"]
          cost_per_unit?: number | null
          created_at?: string | null
          damaged_quantity?: number
          distributed_quantity?: number
          id?: string
          last_updated_by: string
          lost_quantity?: number
          minimum_threshold?: number
          notes?: string | null
          storage_location?: string | null
          total_quantity?: number
          updated_at?: string | null
        }
        Update: {
          available_quantity?: number
          book_id?: string
          condition?: Database["public"]["Enums"]["book_condition"]
          cost_per_unit?: number | null
          created_at?: string | null
          damaged_quantity?: number
          distributed_quantity?: number
          id?: string
          last_updated_by?: string
          lost_quantity?: number
          minimum_threshold?: number
          notes?: string | null
          storage_location?: string | null
          total_quantity?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "book_inventory_book_id_fkey"
            columns: ["book_id"]
            isOneToOne: false
            referencedRelation: "books"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_inventory_last_updated_by_fkey"
            columns: ["last_updated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      books: {
        Row: {
          author: string
          created_at: string | null
          created_by: string
          description: string | null
          id: string
          isbn: string | null
          language: Database["public"]["Enums"]["book_language"]
          publication_year: number | null
          publisher: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          author: string
          created_at?: string | null
          created_by: string
          description?: string | null
          id?: string
          isbn?: string | null
          language?: Database["public"]["Enums"]["book_language"]
          publication_year?: number | null
          publisher?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          author?: string
          created_at?: string | null
          created_by?: string
          description?: string | null
          id?: string
          isbn?: string | null
          language?: Database["public"]["Enums"]["book_language"]
          publication_year?: number | null
          publisher?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "books_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      distribution_photos: {
        Row: {
          caption: string | null
          created_at: string | null
          distribution_id: string
          id: string
          photo_url: string
          uploaded_by: string
        }
        Insert: {
          caption?: string | null
          created_at?: string | null
          distribution_id: string
          id?: string
          photo_url: string
          uploaded_by: string
        }
        Update: {
          caption?: string | null
          created_at?: string | null
          distribution_id?: string
          id?: string
          photo_url?: string
          uploaded_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "distribution_photos_distribution_id_fkey"
            columns: ["distribution_id"]
            isOneToOne: false
            referencedRelation: "book_distributions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "distribution_photos_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_distribution_photos_distribution_id"
            columns: ["distribution_id"]
            isOneToOne: false
            referencedRelation: "book_distributions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_distribution_photos_uploaded_by"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      field_reports: {
        Row: {
          activities_conducted: string[] | null
          activity_dates: string | null
          activity_feedback: Json | null
          activity_type:
            | Database["public"]["Enums"]["field_activity_type"]
            | null
          attendance_id: string | null
          challenges: string | null
          champions_count: number | null
          created_at: string | null
          description: string
          facilitators: Json | null
          female_participants: number | null
          findings: string | null
          follow_up_actions: string | null
          follow_up_required: boolean | null
          gps_coordinates: unknown | null
          id: string
          introduction: string | null
          lessons_learned: string | null
          male_participants: number | null
          notes: string | null
          observations: string | null
          offline_sync: boolean | null
          photos: string[] | null
          recommendations: string | null
          report_date: string | null
          report_type: string
          reported_by: string
          round_table_sessions: number | null
          round_tables_other: number | null
          round_tables_primary: number | null
          round_tables_s1: number | null
          round_tables_s2: number | null
          round_tables_s3: number | null
          round_tables_s4: number | null
          school_id: string | null
          staff_id: string | null
          students_other: number | null
          students_per_session: number | null
          students_primary: number | null
          students_s1: number | null
          students_s2: number | null
          students_s3: number | null
          students_s4: number | null
          title: string
          topics_covered: string[] | null
          total_students: number | null
          updated_at: string | null
          venue_location: string | null
          way_forward: string | null
          wins: string | null
        }
        Insert: {
          activities_conducted?: string[] | null
          activity_dates?: string | null
          activity_feedback?: Json | null
          activity_type?:
            | Database["public"]["Enums"]["field_activity_type"]
            | null
          attendance_id?: string | null
          challenges?: string | null
          champions_count?: number | null
          created_at?: string | null
          description: string
          facilitators?: Json | null
          female_participants?: number | null
          findings?: string | null
          follow_up_actions?: string | null
          follow_up_required?: boolean | null
          gps_coordinates?: unknown | null
          id?: string
          introduction?: string | null
          lessons_learned?: string | null
          male_participants?: number | null
          notes?: string | null
          observations?: string | null
          offline_sync?: boolean | null
          photos?: string[] | null
          recommendations?: string | null
          report_date?: string | null
          report_type: string
          reported_by: string
          round_table_sessions?: number | null
          round_tables_other?: number | null
          round_tables_primary?: number | null
          round_tables_s1?: number | null
          round_tables_s2?: number | null
          round_tables_s3?: number | null
          round_tables_s4?: number | null
          school_id?: string | null
          staff_id?: string | null
          students_other?: number | null
          students_per_session?: number | null
          students_primary?: number | null
          students_s1?: number | null
          students_s2?: number | null
          students_s3?: number | null
          students_s4?: number | null
          title: string
          topics_covered?: string[] | null
          total_students?: number | null
          updated_at?: string | null
          venue_location?: string | null
          way_forward?: string | null
          wins?: string | null
        }
        Update: {
          activities_conducted?: string[] | null
          activity_dates?: string | null
          activity_feedback?: Json | null
          activity_type?:
            | Database["public"]["Enums"]["field_activity_type"]
            | null
          attendance_id?: string | null
          challenges?: string | null
          champions_count?: number | null
          created_at?: string | null
          description?: string
          facilitators?: Json | null
          female_participants?: number | null
          findings?: string | null
          follow_up_actions?: string | null
          follow_up_required?: boolean | null
          gps_coordinates?: unknown | null
          id?: string
          introduction?: string | null
          lessons_learned?: string | null
          male_participants?: number | null
          notes?: string | null
          observations?: string | null
          offline_sync?: boolean | null
          photos?: string[] | null
          recommendations?: string | null
          report_date?: string | null
          report_type?: string
          reported_by?: string
          round_table_sessions?: number | null
          round_tables_other?: number | null
          round_tables_primary?: number | null
          round_tables_s1?: number | null
          round_tables_s2?: number | null
          round_tables_s3?: number | null
          round_tables_s4?: number | null
          school_id?: string | null
          staff_id?: string | null
          students_other?: number | null
          students_per_session?: number | null
          students_primary?: number | null
          students_s1?: number | null
          students_s2?: number | null
          students_s3?: number | null
          students_s4?: number | null
          title?: string
          topics_covered?: string[] | null
          total_students?: number | null
          updated_at?: string | null
          venue_location?: string | null
          way_forward?: string | null
          wins?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "field_reports_attendance_id_fkey"
            columns: ["attendance_id"]
            isOneToOne: false
            referencedRelation: "field_staff_attendance"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_reported_by_fkey"
            columns: ["reported_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      field_staff_attendance: {
        Row: {
          attendance_date: string
          check_in_accuracy: number | null
          check_in_address: string | null
          check_in_location: unknown
          check_in_time: string
          check_out_accuracy: number | null
          check_out_address: string | null
          check_out_location: unknown | null
          check_out_time: string | null
          created_at: string | null
          device_info: Json | null
          distance_from_school: number | null
          id: string
          location_verified: boolean | null
          network_info: Json | null
          notes: string | null
          offline_sync: boolean | null
          school_id: string
          staff_id: string
          status: string | null
          sync_timestamp: string | null
          total_duration_minutes: number | null
          updated_at: string | null
          verification_method: string | null
        }
        Insert: {
          attendance_date: string
          check_in_accuracy?: number | null
          check_in_address?: string | null
          check_in_location: unknown
          check_in_time: string
          check_out_accuracy?: number | null
          check_out_address?: string | null
          check_out_location?: unknown | null
          check_out_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          distance_from_school?: number | null
          id?: string
          location_verified?: boolean | null
          network_info?: Json | null
          notes?: string | null
          offline_sync?: boolean | null
          school_id: string
          staff_id: string
          status?: string | null
          sync_timestamp?: string | null
          total_duration_minutes?: number | null
          updated_at?: string | null
          verification_method?: string | null
        }
        Update: {
          attendance_date?: string
          check_in_accuracy?: number | null
          check_in_address?: string | null
          check_in_location?: unknown
          check_in_time?: string
          check_out_accuracy?: number | null
          check_out_address?: string | null
          check_out_location?: unknown | null
          check_out_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          distance_from_school?: number | null
          id?: string
          location_verified?: boolean | null
          network_info?: Json | null
          notes?: string | null
          offline_sync?: boolean | null
          school_id?: string
          staff_id?: string
          status?: string | null
          sync_timestamp?: string | null
          total_duration_minutes?: number | null
          updated_at?: string | null
          verification_method?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "field_staff_attendance_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_staff_attendance_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          country: string
          created_at: string | null
          division_id: string | null
          id: string
          invitation_accepted_at: string | null
          is_active: boolean | null
          last_password_change: string | null
          name: string
          phone: string | null
          requires_password_change: boolean | null
          role: Database["public"]["Enums"]["user_role"]
        }
        Insert: {
          country?: string
          created_at?: string | null
          division_id?: string | null
          id: string
          invitation_accepted_at?: string | null
          is_active?: boolean | null
          last_password_change?: string | null
          name: string
          phone?: string | null
          requires_password_change?: boolean | null
          role?: Database["public"]["Enums"]["user_role"]
        }
        Update: {
          country?: string
          created_at?: string | null
          division_id?: string | null
          id?: string
          invitation_accepted_at?: string | null
          is_active?: boolean | null
          last_password_change?: string | null
          name?: string
          phone?: string | null
          requires_password_change?: boolean | null
          role?: Database["public"]["Enums"]["user_role"]
        }
        Relationships: [
          {
            foreignKeyName: "profiles_division_id_fkey"
            columns: ["division_id"]
            isOneToOne: false
            referencedRelation: "administrative_divisions"
            referencedColumns: ["id"]
          },
        ]
      }
      school_audit: {
        Row: {
          action: string
          changed_at: string | null
          changed_by: string
          id: string
          new_values: Json | null
          old_values: Json | null
          school_id: string
        }
        Insert: {
          action: string
          changed_at?: string | null
          changed_by: string
          id?: string
          new_values?: Json | null
          old_values?: Json | null
          school_id: string
        }
        Update: {
          action?: string
          changed_at?: string | null
          changed_by?: string
          id?: string
          new_values?: Json | null
          old_values?: Json | null
          school_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_school_audit_changed_by"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_school_audit_school_id"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "school_audit_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      school_champion_teachers: {
        Row: {
          contact_type: string
          created_at: string | null
          email: string | null
          id: string
          is_active: boolean | null
          name: string
          phone: string | null
          school_id: string
          updated_at: string | null
        }
        Insert: {
          contact_type: string
          created_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          phone?: string | null
          school_id: string
          updated_at?: string | null
        }
        Update: {
          contact_type?: string
          created_at?: string | null
          email?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          phone?: string | null
          school_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "school_champion_teachers_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      schools: {
        Row: {
          champion_teacher_count: number | null
          classes_count: number | null
          code: string | null
          contact_phone: string | null
          created_at: string | null
          created_by: string | null
          date_joined_ilead: string | null
          deputy_head_teacher_name: string | null
          distance_to_main_road: string | null
          division_id: string | null
          email: string | null
          head_teacher_name: string | null
          id: string
          infrastructure_notes: string | null
          is_partner_managed: boolean | null
          location_coordinates: unknown | null
          location_description: string | null
          name: string
          nearest_health_center: string | null
          ownership_type: string | null
          partner_name: string | null
          registration_status:
            | Database["public"]["Enums"]["registration_status"]
            | null
          school_category: Database["public"]["Enums"]["school_category"] | null
          school_type: Database["public"]["Enums"]["school_type"]
          streams_per_class: number | null
          student_count: number | null
          teacher_count: number | null
          updated_at: string | null
          year_established: number | null
        }
        Insert: {
          champion_teacher_count?: number | null
          classes_count?: number | null
          code?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by?: string | null
          date_joined_ilead?: string | null
          deputy_head_teacher_name?: string | null
          distance_to_main_road?: string | null
          division_id?: string | null
          email?: string | null
          head_teacher_name?: string | null
          id?: string
          infrastructure_notes?: string | null
          is_partner_managed?: boolean | null
          location_coordinates?: unknown | null
          location_description?: string | null
          name: string
          nearest_health_center?: string | null
          ownership_type?: string | null
          partner_name?: string | null
          registration_status?:
            | Database["public"]["Enums"]["registration_status"]
            | null
          school_category?:
            | Database["public"]["Enums"]["school_category"]
            | null
          school_type: Database["public"]["Enums"]["school_type"]
          streams_per_class?: number | null
          student_count?: number | null
          teacher_count?: number | null
          updated_at?: string | null
          year_established?: number | null
        }
        Update: {
          champion_teacher_count?: number | null
          classes_count?: number | null
          code?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by?: string | null
          date_joined_ilead?: string | null
          deputy_head_teacher_name?: string | null
          distance_to_main_road?: string | null
          division_id?: string | null
          email?: string | null
          head_teacher_name?: string | null
          id?: string
          infrastructure_notes?: string | null
          is_partner_managed?: boolean | null
          location_coordinates?: unknown | null
          location_description?: string | null
          name?: string
          nearest_health_center?: string | null
          ownership_type?: string | null
          partner_name?: string | null
          registration_status?:
            | Database["public"]["Enums"]["registration_status"]
            | null
          school_category?:
            | Database["public"]["Enums"]["school_category"]
            | null
          school_type?: Database["public"]["Enums"]["school_type"]
          streams_per_class?: number | null
          student_count?: number | null
          teacher_count?: number | null
          updated_at?: string | null
          year_established?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "schools_division_id_fkey"
            columns: ["division_id"]
            isOneToOne: false
            referencedRelation: "administrative_divisions"
            referencedColumns: ["id"]
          },
        ]
      }
      staff_audit_log: {
        Row: {
          action: Database["public"]["Enums"]["audit_action"]
          created_at: string | null
          details: Json | null
          id: string
          ip_address: unknown | null
          performed_by: string
          target_email: string | null
          target_user_id: string | null
          user_agent: string | null
        }
        Insert: {
          action: Database["public"]["Enums"]["audit_action"]
          created_at?: string | null
          details?: Json | null
          id?: string
          ip_address?: unknown | null
          performed_by: string
          target_email?: string | null
          target_user_id?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: Database["public"]["Enums"]["audit_action"]
          created_at?: string | null
          details?: Json | null
          id?: string
          ip_address?: unknown | null
          performed_by?: string
          target_email?: string | null
          target_user_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "staff_audit_log_performed_by_fkey"
            columns: ["performed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      staff_location_logs: {
        Row: {
          address_description: string | null
          check_in_status: Database["public"]["Enums"]["check_in_status"]
          check_in_time: string | null
          check_out_time: string | null
          created_at: string | null
          device_info: Json | null
          distance_from_school: number | null
          id: string
          location_accuracy: number | null
          location_coordinates: unknown
          location_verified: boolean | null
          network_info: Json | null
          notes: string | null
          offline_sync: boolean | null
          school_id: string
          session_id: string | null
          staff_id: string
          sync_timestamp: string | null
          total_duration_minutes: number | null
          verification_method: string | null
        }
        Insert: {
          address_description?: string | null
          check_in_status: Database["public"]["Enums"]["check_in_status"]
          check_in_time?: string | null
          check_out_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          distance_from_school?: number | null
          id?: string
          location_accuracy?: number | null
          location_coordinates: unknown
          location_verified?: boolean | null
          network_info?: Json | null
          notes?: string | null
          offline_sync?: boolean | null
          school_id: string
          session_id?: string | null
          staff_id: string
          sync_timestamp?: string | null
          total_duration_minutes?: number | null
          verification_method?: string | null
        }
        Update: {
          address_description?: string | null
          check_in_status?: Database["public"]["Enums"]["check_in_status"]
          check_in_time?: string | null
          check_out_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          distance_from_school?: number | null
          id?: string
          location_accuracy?: number | null
          location_coordinates?: unknown
          location_verified?: boolean | null
          network_info?: Json | null
          notes?: string | null
          offline_sync?: boolean | null
          school_id?: string
          session_id?: string | null
          staff_id?: string
          sync_timestamp?: string | null
          total_duration_minutes?: number | null
          verification_method?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_staff_location_logs_school_id"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_staff_location_logs_staff_id"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_location_logs_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_location_logs_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "attendance_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "staff_location_logs_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      student_attendance: {
        Row: {
          absence_reason: string | null
          attendance_status: Database["public"]["Enums"]["attendance_status"]
          behavior_notes: string | null
          check_in_time: string | null
          check_out_time: string | null
          early_departure_minutes: number | null
          id: string
          late_minutes: number | null
          makeup_session_id: string | null
          makeup_session_required: boolean | null
          parent_notification_time: string | null
          parent_notified: boolean | null
          participation_score: number | null
          recorded_at: string | null
          recorded_by: string
          school_id: string
          session_id: string
          student_id: string
          table_number: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          absence_reason?: string | null
          attendance_status: Database["public"]["Enums"]["attendance_status"]
          behavior_notes?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          early_departure_minutes?: number | null
          id?: string
          late_minutes?: number | null
          makeup_session_id?: string | null
          makeup_session_required?: boolean | null
          parent_notification_time?: string | null
          parent_notified?: boolean | null
          participation_score?: number | null
          recorded_at?: string | null
          recorded_by: string
          school_id: string
          session_id: string
          student_id: string
          table_number?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          absence_reason?: string | null
          attendance_status?: Database["public"]["Enums"]["attendance_status"]
          behavior_notes?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          early_departure_minutes?: number | null
          id?: string
          late_minutes?: number | null
          makeup_session_id?: string | null
          makeup_session_required?: boolean | null
          parent_notification_time?: string | null
          parent_notified?: boolean | null
          participation_score?: number | null
          recorded_at?: string | null
          recorded_by?: string
          school_id?: string
          session_id?: string
          student_id?: string
          table_number?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "student_attendance_makeup_session_id_fkey"
            columns: ["makeup_session_id"]
            isOneToOne: false
            referencedRelation: "attendance_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_attendance_recorded_by_fkey"
            columns: ["recorded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_attendance_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_attendance_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "attendance_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_attendance_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_attendance_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      student_leadership_participation: {
        Row: {
          attendance_percentage: number | null
          certification_date: string | null
          certification_received: boolean | null
          created_at: string | null
          created_by: string
          feedback_comments: string | null
          feedback_rating:
            | Database["public"]["Enums"]["satisfaction_rating"]
            | null
          id: string
          improvement_score: number | null
          leadership_program_id: string
          post_program_score: number | null
          pre_program_score: number | null
          school_id: string
          student_id: string | null
          student_name: string
          updated_at: string | null
        }
        Insert: {
          attendance_percentage?: number | null
          certification_date?: string | null
          certification_received?: boolean | null
          created_at?: string | null
          created_by: string
          feedback_comments?: string | null
          feedback_rating?:
            | Database["public"]["Enums"]["satisfaction_rating"]
            | null
          id?: string
          improvement_score?: number | null
          leadership_program_id: string
          post_program_score?: number | null
          pre_program_score?: number | null
          school_id: string
          student_id?: string | null
          student_name: string
          updated_at?: string | null
        }
        Update: {
          attendance_percentage?: number | null
          certification_date?: string | null
          certification_received?: boolean | null
          created_at?: string | null
          created_by?: string
          feedback_comments?: string | null
          feedback_rating?:
            | Database["public"]["Enums"]["satisfaction_rating"]
            | null
          id?: string
          improvement_score?: number | null
          leadership_program_id?: string
          post_program_score?: number | null
          pre_program_score?: number | null
          school_id?: string
          student_id?: string | null
          student_name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "student_leadership_participation_leadership_program_id_fkey"
            columns: ["leadership_program_id"]
            isOneToOne: false
            referencedRelation: "student_leadership_programs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_training_participation_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_training_participation_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      student_leadership_programs: {
        Row: {
          actual_participants: number | null
          cost: number | null
          created_at: string | null
          created_by: string
          description: string | null
          duration_hours: number | null
          end_date: string
          facilitator_name: string | null
          facilitator_organization: string | null
          funding_source: string | null
          id: string
          program_name: string
          program_type: Database["public"]["Enums"]["leadership_program_type"]
          start_date: string
          target_participants: number | null
          training_materials: Json | null
          updated_at: string | null
          venue: string | null
        }
        Insert: {
          actual_participants?: number | null
          cost?: number | null
          created_at?: string | null
          created_by: string
          description?: string | null
          duration_hours?: number | null
          end_date: string
          facilitator_name?: string | null
          facilitator_organization?: string | null
          funding_source?: string | null
          id?: string
          program_name: string
          program_type: Database["public"]["Enums"]["leadership_program_type"]
          start_date: string
          target_participants?: number | null
          training_materials?: Json | null
          updated_at?: string | null
          venue?: string | null
        }
        Update: {
          actual_participants?: number | null
          cost?: number | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          duration_hours?: number | null
          end_date?: string
          facilitator_name?: string | null
          facilitator_organization?: string | null
          funding_source?: string | null
          id?: string
          program_name?: string
          program_type?: Database["public"]["Enums"]["leadership_program_type"]
          start_date?: string
          target_participants?: number | null
          training_materials?: Json | null
          updated_at?: string | null
          venue?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_student_leadership_programs_created_by"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teacher_training_programs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      students: {
        Row: {
          created_at: string | null
          created_by: string
          date_of_birth: string | null
          enrollment_date: string
          first_name: string
          gender: string | null
          grade_level: number
          guardian_contact: string | null
          guardian_name: string | null
          id: string
          last_name: string
          school_id: string
          status: string | null
          student_number: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          date_of_birth?: string | null
          enrollment_date: string
          first_name: string
          gender?: string | null
          grade_level: number
          guardian_contact?: string | null
          guardian_name?: string | null
          id?: string
          last_name: string
          school_id: string
          status?: string | null
          student_number?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          date_of_birth?: string | null
          enrollment_date?: string
          first_name?: string
          gender?: string | null
          grade_level?: number
          guardian_contact?: string | null
          guardian_name?: string | null
          id?: string
          last_name?: string
          school_id?: string
          status?: string | null
          student_number?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "students_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      task_attachments: {
        Row: {
          created_at: string | null
          file_name: string | null
          file_size: number | null
          file_url: string | null
          id: string
          task_id: string
          uploaded_by: string
        }
        Insert: {
          created_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          task_id: string
          uploaded_by: string
        }
        Update: {
          created_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          task_id?: string
          uploaded_by?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_task_attachments_task_id"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_task_attachments_uploaded_by"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_attachments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_attachments_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      task_comments: {
        Row: {
          comment: string
          created_at: string | null
          id: string
          task_id: string
          user_id: string
        }
        Insert: {
          comment: string
          created_at?: string | null
          id?: string
          task_id: string
          user_id: string
        }
        Update: {
          comment?: string
          created_at?: string | null
          id?: string
          task_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "task_comments_task_id_fkey"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assigned_to: string | null
          created_at: string | null
          created_by: string
          description: string | null
          due_date: string | null
          id: string
          priority: Database["public"]["Enums"]["task_priority"] | null
          school_id: string | null
          status: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          due_date?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["task_priority"] | null
          school_id?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          assigned_to?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          due_date?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["task_priority"] | null
          school_id?: string | null
          status?: Database["public"]["Enums"]["task_status"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      user_invitations: {
        Row: {
          accepted_at: string | null
          created_at: string | null
          created_by: string
          division_id: string | null
          email: string
          expires_at: string
          id: string
          invitation_token: string
          name: string
          phone: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string | null
          created_by: string
          division_id?: string | null
          email: string
          expires_at: string
          id?: string
          invitation_token: string
          name: string
          phone?: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Update: {
          accepted_at?: string | null
          created_at?: string | null
          created_by?: string
          division_id?: string | null
          email?: string
          expires_at?: string
          id?: string
          invitation_token?: string
          name?: string
          phone?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_invitations_division_id_fkey"
            columns: ["division_id"]
            isOneToOne: false
            referencedRelation: "administrative_divisions"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      enhanced_field_reports: {
        Row: {
          activities_conducted: string[] | null
          activity_dates: string | null
          activity_feedback: Json | null
          activity_type:
            | Database["public"]["Enums"]["field_activity_type"]
            | null
          attendance_id: string | null
          challenges: string | null
          champions_count: number | null
          check_in_address: string | null
          check_in_time: string | null
          check_out_address: string | null
          check_out_time: string | null
          created_at: string | null
          description: string | null
          facilitators: Json | null
          female_participants: number | null
          findings: string | null
          follow_up_actions: string | null
          follow_up_required: boolean | null
          gps_coordinates: unknown | null
          id: string | null
          introduction: string | null
          lessons_learned: string | null
          male_participants: number | null
          notes: string | null
          observations: string | null
          offline_sync: boolean | null
          photos: string[] | null
          recommendations: string | null
          report_date: string | null
          report_type: string | null
          reported_by: string | null
          round_table_sessions: number | null
          round_tables_other: number | null
          round_tables_primary: number | null
          round_tables_s1: number | null
          round_tables_s2: number | null
          round_tables_s3: number | null
          round_tables_s4: number | null
          school_district: string | null
          school_id: string | null
          school_name: string | null
          school_sub_county: string | null
          staff_id: string | null
          staff_name: string | null
          students_other: number | null
          students_per_session: number | null
          students_primary: number | null
          students_s1: number | null
          students_s2: number | null
          students_s3: number | null
          students_s4: number | null
          title: string | null
          topics_covered: string[] | null
          total_participants_calculated: number | null
          total_round_tables_calculated: number | null
          total_students: number | null
          total_students_calculated: number | null
          updated_at: string | null
          venue_location: string | null
          way_forward: string | null
          wins: string | null
        }
        Relationships: [
          {
            foreignKeyName: "field_reports_attendance_id_fkey"
            columns: ["attendance_id"]
            isOneToOne: false
            referencedRelation: "field_staff_attendance"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_reported_by_fkey"
            columns: ["reported_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_school_id_fkey"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "field_reports_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      add_book_distribution: {
        Args: {
          p_school_id: string
          p_inventory_id: string
          p_quantity: number
          p_supervisor_id: string
          p_notes: string
          p_book_title: string
          p_status?: string
          p_distribution_date?: string
        }
        Returns: string
      }
      add_book_with_inventory: {
        Args: {
          p_title: string
          p_author?: string
          p_isbn?: string
          p_publication_year?: number
          p_language?: Database["public"]["Enums"]["book_language"]
          p_publisher?: string
          p_description?: string
          p_total_quantity?: number
          p_condition?: Database["public"]["Enums"]["book_condition"]
          p_storage_location?: string
          p_cost_per_unit?: number
          p_minimum_threshold?: number
          p_notes?: string
        }
        Returns: string
      }
      add_school: {
        Args: {
          p_name: string
          p_code: string
          p_school_type: Database["public"]["Enums"]["school_type"]
          p_student_count: number
          p_teacher_count: number
          p_contact_phone: string
          p_email: string
          p_division_id: string
          p_created_by: string
        }
        Returns: string
      }
      add_school_enhanced: {
        Args: {
          p_name: string
          p_school_type: Database["public"]["Enums"]["school_type"]
          p_division_id: string
          p_student_count: number
          p_champion_teacher_count: number
          p_code?: string
          p_school_category?: Database["public"]["Enums"]["school_category"]
          p_contact_phone?: string
          p_email?: string
          p_head_teacher_name?: string
          p_head_teacher_phone?: string
          p_head_teacher_email?: string
          p_deputy_head_teacher_name?: string
          p_deputy_head_teacher_phone?: string
          p_deputy_head_teacher_email?: string
          p_date_joined_ilead?: string
          p_ownership_type?: string
          p_location_coordinates?: unknown
          p_is_partner_managed?: boolean
          p_partner_name?: string
          p_field_staff_id?: string
          p_champion_teachers?: Json
          p_assistant_champion_teachers?: Json
        }
        Returns: string
      }
      add_task_comment: {
        Args: { p_task_id: string; p_comment: string; p_user_id?: string }
        Returns: {
          id: string
          task_id: string
          user_id: string
          user_name: string
          comment: string
          created_at: string
        }[]
      }
      assign_task: {
        Args: {
          p_task_id: string
          p_assigned_to: string
          p_assigned_by?: string
        }
        Returns: boolean
      }
      bulk_record_attendance: {
        Args: { p_session_id: string; p_attendance_records: Json }
        Returns: number
      }
      bulk_update_school_status: {
        Args: {
          p_school_ids: string[]
          p_new_status: Database["public"]["Enums"]["registration_status"]
        }
        Returns: number
      }
      calculate_attendance_analytics: {
        Args: {
          p_school_id: string
          p_period_start: string
          p_period_end: string
          p_analysis_period?: string
          p_student_id?: string
        }
        Returns: string
      }
      calculate_distance_meters: {
        Args: { lat1: number; lon1: number; lat2: number; lon2: number }
        Returns: number
      }
      calculate_total_participants: {
        Args: { report_id: string }
        Returns: number
      }
      cleanup_old_activities: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_schools_data: {
        Args: Record<PropertyKey, never>
        Returns: {
          step_name: string
          records_deleted: number
          details: string
        }[]
      }
      create_attendance_session: {
        Args: {
          p_session_name: string
          p_session_type: Database["public"]["Enums"]["session_type"]
          p_school_id: string
          p_session_date: string
          p_start_time: string
          p_grade_level?: number
          p_subject?: string
          p_teacher_name?: string
          p_end_time?: string
          p_planned_duration_minutes?: number
          p_location?: string
          p_max_capacity?: number
          p_round_tables_count?: number
          p_session_description?: string
          p_learning_objectives?: string[]
          p_materials_needed?: string[]
        }
        Returns: string
      }
      create_task: {
        Args: {
          p_title: string
          p_description?: string
          p_priority?: Database["public"]["Enums"]["task_priority"]
          p_due_date?: string
          p_assigned_to?: string
          p_school_id?: string
        }
        Returns: string
      }
      create_user_invitation: {
        Args: {
          p_email: string
          p_name: string
          p_role: Database["public"]["Enums"]["user_role"]
          p_division_id?: string
          p_phone?: string
        }
        Returns: Json
      }
      delete_school: {
        Args: { p_school_id: string }
        Returns: boolean
      }
      field_staff_checkin: {
        Args: {
          p_school_id: string
          p_latitude: number
          p_longitude: number
          p_accuracy?: number
          p_address?: string
          p_verification_method?: string
          p_device_info?: Json
          p_network_info?: Json
          p_offline_sync?: boolean
        }
        Returns: string
      }
      field_staff_checkout: {
        Args: {
          p_attendance_id: string
          p_activity_type: Database["public"]["Enums"]["field_activity_type"]
          p_latitude?: number
          p_longitude?: number
          p_accuracy?: number
          p_address?: string
          p_notes?: string
          p_round_table_sessions?: number
          p_total_students?: number
          p_students_per_session?: number
          p_activities_conducted?: string[]
          p_topics_covered?: string[]
          p_challenges?: string
          p_wins?: string
          p_observations?: string
          p_lessons_learned?: string
          p_follow_up_required?: boolean
          p_follow_up_actions?: string
          p_photos?: string[]
          p_offline_sync?: boolean
        }
        Returns: string
      }
      get_activity_stats: {
        Args: { p_days?: number }
        Returns: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          count: number
          latest_activity: string
        }[]
      }
      get_admin_divisions: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          district: string
          sub_county: string
          parish: string
          village: string
        }[]
      }
      get_all_staff: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          email: string
          role: Database["public"]["Enums"]["user_role"]
          division_id: string
          division_name: string
          phone: string
          country: string
          is_active: boolean
          requires_password_change: boolean
          last_password_change: string
          invitation_accepted_at: string
          created_at: string
        }[]
      }
      get_book_distributions: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          school_id: string
          school_name: string
          book_title: string
          quantity: number
          supervisor_id: string
          supervisor_name: string
          delivery_date: string
          notes: string
          status: string
        }[]
      }
      get_book_inventory: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          book_title: string
          quantity_available: number
          language: string
        }[]
      }
      get_books_with_inventory: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          title: string
          author: string
          isbn: string
          publication_year: number
          language: Database["public"]["Enums"]["book_language"]
          publisher: string
          description: string
          total_quantity: number
          available_quantity: number
          distributed_quantity: number
          damaged_quantity: number
          lost_quantity: number
          minimum_threshold: number
          condition: Database["public"]["Enums"]["book_condition"]
          storage_location: string
          cost_per_unit: number
          inventory_notes: string
          created_at: string
          updated_at: string
        }[]
      }
      get_distribution_statistics: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_distributions: number
          total_books_distributed: number
          distributions_this_month: number
          books_this_month: number
          active_distributions: number
          schools_served: number
          top_books: Json
        }[]
      }
      get_field_staff_attendance_status: {
        Args: { p_date?: string; p_staff_id?: string }
        Returns: {
          staff_id: string
          staff_name: string
          attendance_date: string
          total_check_ins: number
          current_status: string
          current_school_name: string
          check_in_time: string
          hours_worked: number
          schools_visited_today: number
        }[]
      }
      get_field_staff_members: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          phone: string
          division_name: string
        }[]
      }
      get_field_staff_timesheets: {
        Args: { p_date?: string; p_staff_id?: string }
        Returns: {
          timesheet_id: string
          staff_id: string
          staff_name: string
          staff_role: string
          timesheet_date: string
          total_work_hours: number
          total_schools_visited: number
          total_students_reached: number
          total_sessions_conducted: number
          schools_visited: Json
          key_achievements: string
          main_challenges: string
          timesheet_status: string
          productivity_score: number
          submitted_at: string
        }[]
      }
      get_recent_activities: {
        Args: { p_limit?: number; p_offset?: number }
        Returns: {
          id: string
          activity_type: Database["public"]["Enums"]["activity_type"]
          user_id: string
          user_name: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          entity_id: string
          description: string
          metadata: Json
          created_at: string
          entity_details: Json
        }[]
      }
      get_recent_tasks: {
        Args: { p_limit?: number }
        Returns: {
          id: string
          title: string
          description: string
          priority: Database["public"]["Enums"]["task_priority"]
          status: Database["public"]["Enums"]["task_status"]
          due_date: string
          assigned_to: string
          assigned_to_name: string
          created_by: string
          created_by_name: string
          school_id: string
          school_name: string
          created_at: string
          updated_at: string
        }[]
      }
      get_school_statistics: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_schools: number
          by_type: Json
          by_status: Json
          by_district: Json
          total_students: number
          total_teachers: number
          avg_student_teacher_ratio: number
        }[]
      }
      get_school_with_contacts: {
        Args: { p_school_id: string }
        Returns: Json
      }
      get_schools_filtered: {
        Args: {
          p_search_term?: string
          p_school_type?: Database["public"]["Enums"]["school_type"]
          p_registration_status?: Database["public"]["Enums"]["registration_status"]
          p_district?: string
          p_sort_by?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: {
          id: string
          name: string
          code: string
          school_type: Database["public"]["Enums"]["school_type"]
          student_count: number
          teacher_count: number
          contact_phone: string
          email: string
          district: string
          sub_county: string
          registration_status: Database["public"]["Enums"]["registration_status"]
          classes_count: number
          streams_per_class: number
          head_teacher_name: string
          deputy_head_teacher_name: string
          year_established: number
          ownership_type: string
          location_description: string
          nearest_health_center: string
          distance_to_main_road: string
          infrastructure_notes: string
          created_at: string
          updated_at: string
          total_count: number
        }[]
      }
      get_schools_with_divisions: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          code: string
          school_type: Database["public"]["Enums"]["school_type"]
          student_count: number
          teacher_count: number
          contact_phone: string
          email: string
          district: string
          sub_county: string
          registration_status: Database["public"]["Enums"]["registration_status"]
        }[]
      }
      get_staff_with_emails: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          email: string
          role: Database["public"]["Enums"]["user_role"]
          division_id: string
          division_name: string
          phone: string
          country: string
          is_active: boolean
          created_at: string
        }[]
      }
      get_student_leadership_effectiveness: {
        Args: {
          p_school_id?: string
          p_program_type?: Database["public"]["Enums"]["leadership_program_type"]
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          leadership_program_id: string
          program_name: string
          program_type: Database["public"]["Enums"]["leadership_program_type"]
          total_participants: number
          completion_rate: number
          avg_improvement_score: number
          certification_rate: number
          avg_satisfaction_rating: number
          effectiveness_score: number
        }[]
      }
      get_task_details: {
        Args: { p_task_id: string }
        Returns: {
          id: string
          title: string
          description: string
          priority: Database["public"]["Enums"]["task_priority"]
          status: Database["public"]["Enums"]["task_status"]
          due_date: string
          assigned_to: string
          assigned_to_name: string
          created_by: string
          created_by_name: string
          school_id: string
          school_name: string
          created_at: string
          updated_at: string
          comments: Json
          attachments: Json
        }[]
      }
      get_tasks: {
        Args: {
          p_user_id?: string
          p_status_filter?: Database["public"]["Enums"]["task_status"]
          p_assigned_filter?: string
        }
        Returns: {
          id: string
          title: string
          description: string
          priority: Database["public"]["Enums"]["task_priority"]
          status: Database["public"]["Enums"]["task_status"]
          due_date: string
          assigned_to: string
          assigned_to_name: string
          created_by: string
          created_by_name: string
          school_id: string
          school_name: string
          created_at: string
          updated_at: string
          comment_count: number
        }[]
      }
      get_tasks_optimized: {
        Args: {
          p_user_id?: string
          p_status_filter?: Database["public"]["Enums"]["task_status"]
          p_assigned_filter?: string
          p_include_comments?: boolean
          p_limit?: number
        }
        Returns: {
          id: string
          title: string
          description: string
          priority: Database["public"]["Enums"]["task_priority"]
          status: Database["public"]["Enums"]["task_status"]
          due_date: string
          assigned_to: string
          assigned_to_name: string
          created_by: string
          created_by_name: string
          school_id: string
          school_name: string
          created_at: string
          updated_at: string
          comment_count: number
        }[]
      }
      get_user_activities: {
        Args: { p_user_id: string; p_limit?: number }
        Returns: {
          id: string
          activity_type: Database["public"]["Enums"]["activity_type"]
          user_id: string
          user_name: string
          entity_type: Database["public"]["Enums"]["entity_type"]
          entity_id: string
          description: string
          metadata: Json
          created_at: string
          entity_details: Json
        }[]
      }
      get_user_profile: {
        Args: { user_id: string }
        Returns: {
          id: string
          name: string
          role: Database["public"]["Enums"]["user_role"]
          division_id: string
          country: string
          phone: string
          is_active: boolean
        }[]
      }
      log_activity: {
        Args: {
          p_activity_type: Database["public"]["Enums"]["activity_type"]
          p_user_id: string
          p_entity_type: Database["public"]["Enums"]["entity_type"]
          p_entity_id: string
          p_description: string
          p_metadata?: Json
        }
        Returns: string
      }
      preview_schools_cleanup: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          records_to_delete: number
          sample_data: string
        }[]
      }
      record_student_attendance: {
        Args: {
          p_session_id: string
          p_student_id: string
          p_attendance_status: Database["public"]["Enums"]["attendance_status"]
          p_check_in_time?: string
          p_late_minutes?: number
          p_table_number?: number
          p_participation_score?: number
          p_behavior_notes?: string
          p_absence_reason?: string
        }
        Returns: string
      }
      staff_gps_checkin: {
        Args: {
          p_school_id: string
          p_latitude: number
          p_longitude: number
          p_session_id?: string
          p_accuracy?: number
          p_address_description?: string
          p_verification_method?: string
          p_device_info?: Json
          p_network_info?: Json
        }
        Returns: string
      }
      toggle_user_status: {
        Args: { p_user_id: string; p_is_active: boolean }
        Returns: boolean
      }
      update_book_inventory: {
        Args: {
          p_inventory_id: string
          p_total_quantity: number
          p_available_quantity: number
          p_distributed_quantity: number
          p_damaged_quantity: number
          p_lost_quantity: number
          p_condition?: Database["public"]["Enums"]["book_condition"]
          p_storage_location?: string
          p_cost_per_unit?: number
          p_minimum_threshold?: number
          p_notes?: string
        }
        Returns: boolean
      }
      update_distribution_status: {
        Args: { p_distribution_id: string; p_status: string; p_notes?: string }
        Returns: boolean
      }
      update_school: {
        Args: {
          p_school_id: string
          p_name?: string
          p_code?: string
          p_school_type?: Database["public"]["Enums"]["school_type"]
          p_student_count?: number
          p_teacher_count?: number
          p_contact_phone?: string
          p_email?: string
          p_division_id?: string
          p_classes_count?: number
          p_streams_per_class?: number
          p_head_teacher_name?: string
          p_deputy_head_teacher_name?: string
          p_year_established?: number
          p_ownership_type?: string
          p_location_description?: string
          p_nearest_health_center?: string
          p_distance_to_main_road?: string
          p_infrastructure_notes?: string
          p_registration_status?: Database["public"]["Enums"]["registration_status"]
        }
        Returns: boolean
      }
      update_task_status: {
        Args: {
          p_task_id: string
          p_new_status: Database["public"]["Enums"]["task_status"]
          p_user_id?: string
        }
        Returns: boolean
      }
      update_user_role: {
        Args: {
          p_user_id: string
          p_new_role: Database["public"]["Enums"]["user_role"]
        }
        Returns: boolean
      }
      validate_activity_feedback: {
        Args: { feedback_data: Json }
        Returns: boolean
      }
      validate_facilitator_data: {
        Args: { facilitators_data: Json }
        Returns: boolean
      }
    }
    Enums: {
      activity_type:
        | "task_created"
        | "task_updated"
        | "task_completed"
        | "distribution_logged"
        | "school_added"
        | "comment_added"
      assessment_type:
        | "baseline"
        | "midterm"
        | "endline"
        | "quarterly"
        | "annual"
      attendance_status: "present" | "absent" | "late" | "excused" | "partial"
      audit_action:
        | "user_created"
        | "user_updated"
        | "user_deleted"
        | "role_changed"
        | "status_changed"
        | "password_reset"
        | "login_attempt"
        | "permission_granted"
        | "permission_revoked"
      book_category:
        | "mathematics"
        | "english"
        | "science"
        | "social_studies"
        | "religious_education"
        | "physical_education"
        | "art"
        | "music"
        | "local_language"
        | "life_skills"
        | "other"
      book_condition: "new" | "good" | "fair" | "poor"
      book_language:
        | "english"
        | "luganda"
        | "runyankole"
        | "ateso"
        | "luo"
        | "lugbara"
        | "runyoro"
        | "lusoga"
        | "other"
      check_in_status: "checked_in" | "checked_out" | "break" | "active"
      distribution_status:
        | "planned"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "delayed"
      entity_type: "task" | "distribution" | "school" | "comment"
      feedback_type:
        | "student"
        | "parent"
        | "teacher"
        | "community_member"
        | "school_administrator"
      field_activity_type:
        | "leadership_training"
        | "school_visit"
        | "community_engagement"
        | "assessment"
        | "meeting"
        | "other"
        | "round_table_session"
      field_report_status: "draft" | "submitted" | "reviewed" | "approved"
      grade_level:
        | "p1"
        | "p2"
        | "p3"
        | "p4"
        | "p5"
        | "p6"
        | "p7"
        | "s1"
        | "s2"
        | "s3"
        | "s4"
        | "s5"
        | "s6"
        | "nursery"
        | "baby_class"
        | "middle_class"
        | "top_class"
      improvement_type:
        | "infrastructure"
        | "equipment"
        | "resources"
        | "facilities"
        | "technology"
        | "furniture"
        | "utilities"
      leadership_program_type:
        | "leadership_skills"
        | "communication"
        | "teamwork"
        | "problem_solving"
        | "critical_thinking"
        | "public_speaking"
        | "project_management"
        | "entrepreneurship"
        | "civic_engagement"
        | "peer_mentoring"
      registration_status: "registered" | "pending" | "unregistered"
      satisfaction_rating:
        | "very_dissatisfied"
        | "dissatisfied"
        | "neutral"
        | "satisfied"
        | "very_satisfied"
      school_category: "day" | "boarding" | "both"
      school_type:
        | "primary"
        | "secondary"
        | "tertiary"
        | "vocational"
        | "university"
      session_type:
        | "class"
        | "leadership_program"
        | "training"
        | "assessment"
        | "meeting"
        | "other"
      subject_type:
        | "literacy"
        | "numeracy"
        | "english"
        | "mathematics"
        | "science"
        | "social_studies"
        | "life_skills"
      task_priority: "low" | "medium" | "high" | "urgent"
      task_status: "pending" | "in_progress" | "completed" | "cancelled"
      user_role: "admin" | "program_officer" | "field_staff"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      activity_type: [
        "task_created",
        "task_updated",
        "task_completed",
        "distribution_logged",
        "school_added",
        "comment_added",
      ],
      assessment_type: [
        "baseline",
        "midterm",
        "endline",
        "quarterly",
        "annual",
      ],
      attendance_status: ["present", "absent", "late", "excused", "partial"],
      audit_action: [
        "user_created",
        "user_updated",
        "user_deleted",
        "role_changed",
        "status_changed",
        "password_reset",
        "login_attempt",
        "permission_granted",
        "permission_revoked",
      ],
      book_category: [
        "mathematics",
        "english",
        "science",
        "social_studies",
        "religious_education",
        "physical_education",
        "art",
        "music",
        "local_language",
        "life_skills",
        "other",
      ],
      book_condition: ["new", "good", "fair", "poor"],
      book_language: [
        "english",
        "luganda",
        "runyankole",
        "ateso",
        "luo",
        "lugbara",
        "runyoro",
        "lusoga",
        "other",
      ],
      check_in_status: ["checked_in", "checked_out", "break", "active"],
      distribution_status: [
        "planned",
        "in_progress",
        "completed",
        "cancelled",
        "delayed",
      ],
      entity_type: ["task", "distribution", "school", "comment"],
      feedback_type: [
        "student",
        "parent",
        "teacher",
        "community_member",
        "school_administrator",
      ],
      field_activity_type: [
        "leadership_training",
        "school_visit",
        "community_engagement",
        "assessment",
        "meeting",
        "other",
        "round_table_session",
      ],
      field_report_status: ["draft", "submitted", "reviewed", "approved"],
      grade_level: [
        "p1",
        "p2",
        "p3",
        "p4",
        "p5",
        "p6",
        "p7",
        "s1",
        "s2",
        "s3",
        "s4",
        "s5",
        "s6",
        "nursery",
        "baby_class",
        "middle_class",
        "top_class",
      ],
      improvement_type: [
        "infrastructure",
        "equipment",
        "resources",
        "facilities",
        "technology",
        "furniture",
        "utilities",
      ],
      leadership_program_type: [
        "leadership_skills",
        "communication",
        "teamwork",
        "problem_solving",
        "critical_thinking",
        "public_speaking",
        "project_management",
        "entrepreneurship",
        "civic_engagement",
        "peer_mentoring",
      ],
      registration_status: ["registered", "pending", "unregistered"],
      satisfaction_rating: [
        "very_dissatisfied",
        "dissatisfied",
        "neutral",
        "satisfied",
        "very_satisfied",
      ],
      school_category: ["day", "boarding", "both"],
      school_type: [
        "primary",
        "secondary",
        "tertiary",
        "vocational",
        "university",
      ],
      session_type: [
        "class",
        "leadership_program",
        "training",
        "assessment",
        "meeting",
        "other",
      ],
      subject_type: [
        "literacy",
        "numeracy",
        "english",
        "mathematics",
        "science",
        "social_studies",
        "life_skills",
      ],
      task_priority: ["low", "medium", "high", "urgent"],
      task_status: ["pending", "in_progress", "completed", "cancelled"],
      user_role: ["admin", "program_officer", "field_staff"],
    },
  },
} as const
