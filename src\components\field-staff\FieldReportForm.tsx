import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  Target,
  AlertTriangle,
  Trophy,
  Eye,
  BookOpen,
  Plus,
  X,
  Loader2,
  AlertCircle,
  Users,
  Calendar,
  MapPin
} from 'lucide-react';
import OptimizedPhotoUpload, { PhotoUploadItem } from './OptimizedPhotoUpload';
import {
  validateField,
  validateFieldReport,
  sanitizeFieldReportData,
  FieldReportFormData,
  FieldValidationResult,
  FacilitatorInfo,
  ActivityFeedback,
  EDUCATION_LEVELS,
  calculateTotalStudents,
  calculateTotalRoundTables
} from '@/utils/fieldReportValidation';
import {
  ALL_LESSON_TOPICS,
  getLessonsByCategory,
  LESSON_CATEGORIES,
  LessonTopic
} from '@/constants/lessonTopics';
import { useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useSchools } from '@/hooks/useSchools';

// Use the database field_activity_type enum
type FieldActivityType = 'round_table_session' | 'school_visit' | 'meeting' | 'other';

// Form-specific interface for react-hook-form
interface FormData {
  activity_type: FieldActivityType;
  round_table_sessions_count: number;
  total_students_attended: number;
  students_per_session: number;
  challenges_encountered: string;
  wins_achieved: string;
  lessons_learned: string;
  follow_up_required: boolean;
  follow_up_actions: string;

  // Enhanced fields from migration
  venue_location: string;
  activity_dates: string;
  male_participants: number;
  female_participants: number;
  students_primary: number;
  students_s1: number;
  students_s2: number;
  students_s3: number;
  students_s4: number;
  students_other: number;
  champions_count: number;
  round_tables_primary: number;
  round_tables_s1: number;
  round_tables_s2: number;
  round_tables_s3: number;
  round_tables_s4: number;
  round_tables_other: number;
  introduction: string;
  recommendations: string;
}

interface FieldReportFormProps {
  onSubmit: (data: FieldReportFormData) => void;
  isSubmitting?: boolean;
  defaultValues?: Partial<FieldReportFormData>;
}

const FieldReportForm: React.FC<FieldReportFormProps> = ({
  onSubmit,
  isSubmitting = false,
  defaultValues = {}
}) => {
  const [activities, setActivities] = useState<string[]>(defaultValues.activities_conducted || []);
  const [topics, setTopics] = useState<string[]>(defaultValues.topics_covered || []);
  const [newActivity, setNewActivity] = useState('');
  const [newTopic, setNewTopic] = useState('');
  const [photos, setPhotos] = useState<PhotoUploadItem[]>([]);
  const [fieldValidations, setFieldValidations] = useState<Record<string, FieldValidationResult>>({});
  const [formWarnings, setFormWarnings] = useState<string[]>([]);

  // Enhanced form state
  const [facilitators, setFacilitators] = useState<FacilitatorInfo[]>(defaultValues.facilitators || []);
  const [activityFeedback, setActivityFeedback] = useState<ActivityFeedback[]>(defaultValues.activity_feedback || []);
  const [showEnhancedFields, setShowEnhancedFields] = useState(false);

  // Round tables state - track which education levels are selected
  const [selectedEducationLevels, setSelectedEducationLevels] = useState<string[]>([]);
  const [selectedLessonCategory, setSelectedLessonCategory] = useState<'iChoose' | 'iLead' | 'iDo' | ''>('');

  // Get current attendance to populate venue location
  const { data: currentAttendance } = useCurrentAttendance();
  const { data: schools } = useSchools();

  // Get the current school name
  const currentSchool = schools?.find(school => school.id === currentAttendance?.school_id);
  const currentSchoolName = currentSchool?.name || '';

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      activity_type: defaultValues?.activity_type || 'round_table_session',
      round_table_sessions_count: defaultValues?.round_table_sessions_count || 0,
      total_students_attended: defaultValues?.total_students_attended || 0,
      students_per_session: defaultValues?.students_per_session || 8,
      challenges_encountered: defaultValues?.challenges_encountered || '',
      wins_achieved: defaultValues?.wins_achieved || '',
      lessons_learned: defaultValues?.lessons_learned || '',
      follow_up_required: defaultValues?.follow_up_required || false,
      follow_up_actions: defaultValues?.follow_up_actions || '',

      // Enhanced fields
      venue_location: defaultValues?.venue_location || '',
      activity_dates: defaultValues?.activity_dates || '',
      male_participants: defaultValues?.male_participants || 0,
      female_participants: defaultValues?.female_participants || 0,
      students_primary: defaultValues?.students_primary || 0,
      students_s1: defaultValues?.students_s1 || 0,
      students_s2: defaultValues?.students_s2 || 0,
      students_s3: defaultValues?.students_s3 || 0,
      students_s4: defaultValues?.students_s4 || 0,
      students_other: defaultValues?.students_other || 0,
      champions_count: defaultValues?.champions_count || 0,
      round_tables_primary: defaultValues?.round_tables_primary || 0,
      round_tables_s1: defaultValues?.round_tables_s1 || 0,
      round_tables_s2: defaultValues?.round_tables_s2 || 0,
      round_tables_s3: defaultValues?.round_tables_s3 || 0,
      round_tables_s4: defaultValues?.round_tables_s4 || 0,
      round_tables_other: defaultValues?.round_tables_other || 0,
      introduction: defaultValues?.introduction || '',
      recommendations: defaultValues?.recommendations || '',
    }
  });

  const followUpRequired = watch('follow_up_required');
  const activityType = watch('activity_type');

  // Watch enhanced fields for calculations
  const roundTablesPrimary = watch('round_tables_primary') || 0;
  const roundTablesS1 = watch('round_tables_s1') || 0;
  const roundTablesS2 = watch('round_tables_s2') || 0;
  const roundTablesS3 = watch('round_tables_s3') || 0;
  const roundTablesS4 = watch('round_tables_s4') || 0;
  const roundTablesOther = watch('round_tables_other') || 0;
  const totalStudentsAttended = watch('total_students_attended') || 0;

  // Calculate total round tables and students absent
  const totalRoundTables = roundTablesPrimary + roundTablesS1 + roundTablesS2 +
                          roundTablesS3 + roundTablesS4 + roundTablesOther;
  const expectedStudents = totalRoundTables * 8; // 8 students per round table
  const studentsAbsent = Math.max(0, expectedStudents - totalStudentsAttended);

  // Real-time validation for form fields
  const validateFormField = (fieldName: keyof FieldReportFormData, value: any) => {
    const currentFormData = {
      ...getValues(),
      activities_conducted: activities,
      topics_covered: topics,
      photos: photos as any // Type assertion for PhotoUploadItem[]
    } as FieldReportFormData;
    const validation = validateField(fieldName, value, currentFormData);

    setFieldValidations(prev => ({
      ...prev,
      [fieldName]: validation
    }));

    return validation;
  };

  // Validate field on blur or change
  const handleFieldValidation = (fieldName: keyof FormData) => {
    const value = getValues(fieldName);
    validateFormField(fieldName as keyof FieldReportFormData, value);
  };

  const addActivity = () => {
    if (newActivity.trim()) {
      // Validate the new activity before adding
      const validation = validateField('activities_conducted', [...activities, newActivity.trim()]);
      if (!validation.isValid) {
        setFieldValidations(prev => ({
          ...prev,
          activities_conducted: validation
        }));
        return;
      }

      const updatedActivities = [...activities, newActivity.trim()];
      setActivities(updatedActivities);
      setNewActivity('');

      // Clear any previous validation errors
      setFieldValidations(prev => ({
        ...prev,
        activities_conducted: { isValid: true }
      }));
    }
  };

  const removeActivity = (index: number) => {
    const updatedActivities = activities.filter((_, i) => i !== index);
    setActivities(updatedActivities);
  };

  const addTopic = () => {
    if (newTopic.trim()) {
      // Validate the new topic before adding
      const validation = validateField('topics_covered', [...topics, newTopic.trim()]);
      if (!validation.isValid) {
        setFieldValidations(prev => ({
          ...prev,
          topics_covered: validation
        }));
        return;
      }

      const updatedTopics = [...topics, newTopic.trim()];
      setTopics(updatedTopics);
      setNewTopic('');

      // Clear any previous validation errors
      setFieldValidations(prev => ({
        ...prev,
        topics_covered: { isValid: true }
      }));
    }
  };

  const removeTopic = (index: number) => {
    const updatedTopics = topics.filter((_, i) => i !== index);
    setTopics(updatedTopics);
  };

  // Education level management
  const addEducationLevel = (level: string) => {
    if (!selectedEducationLevels.includes(level)) {
      setSelectedEducationLevels(prev => [...prev, level]);
    }
  };

  const removeEducationLevel = (level: string) => {
    setSelectedEducationLevels(prev => prev.filter(l => l !== level));
    // Reset the corresponding round table count
    const fieldName = `round_tables_${level}` as keyof FormData;
    setValue(fieldName, 0);
  };

  // Handle photo changes from OptimizedPhotoUpload
  const handlePhotosChanged = (newPhotos: PhotoUploadItem[]) => {
    setPhotos(newPhotos);
  };

  // Facilitator management functions
  const addFacilitator = () => {
    setFacilitators(prev => [...prev, { name: '', mobile: '', email: '' }]);
  };

  const updateFacilitator = (index: number, field: keyof FacilitatorInfo, value: string) => {
    setFacilitators(prev => prev.map((facilitator, i) =>
      i === index ? { ...facilitator, [field]: value } : facilitator
    ));
  };

  const removeFacilitator = (index: number) => {
    setFacilitators(prev => prev.filter((_, i) => i !== index));
  };

  // Activity feedback management functions
  const addActivityFeedback = () => {
    setActivityFeedback(prev => [...prev, { topic: '', what_worked_well: '', participant_comments: '' }]);
  };

  const updateActivityFeedback = (index: number, field: keyof ActivityFeedback, value: string) => {
    setActivityFeedback(prev => prev.map((feedback, i) =>
      i === index ? { ...feedback, [field]: value } : feedback
    ));
  };

  const removeActivityFeedback = (index: number) => {
    setActivityFeedback(prev => prev.filter((_, i) => i !== index));
  };

  const onFormSubmit = (data: FormData) => {
    // Prepare form data
    const formData: FieldReportFormData = {
      ...data,
      activities_conducted: activities,
      topics_covered: topics,
      photos: photos as any, // Type assertion for PhotoUploadItem[]
      facilitators: facilitators,
      activity_feedback: activityFeedback,
    };

    // Perform comprehensive validation
    const validation = validateFieldReport(formData);

    if (!validation.isValid) {
      // Set field errors for display
      const newFieldValidations: Record<string, FieldValidationResult> = {};
      Object.entries(validation.fieldErrors).forEach(([field, error]) => {
        newFieldValidations[field] = { isValid: false, error };
      });
      setFieldValidations(newFieldValidations);

      // Show general error message
      console.error('Form validation failed:', validation.errors);
      return;
    }

    // Set warnings if any
    if (validation.warnings.length > 0) {
      setFormWarnings(validation.warnings);
    }

    // Sanitize data before submission
    const sanitizedData = sanitizeFieldReportData(formData);

    onSubmit(sanitizedData);
  };

  // Helper component for displaying field validation messages
  const FieldValidationMessage = ({ fieldName }: { fieldName: string }) => {
    const validation = fieldValidations[fieldName];
    if (!validation) return null;

    if (!validation.isValid && validation.error) {
      return (
        <div className="flex items-center gap-1 text-sm text-red-600 mt-1">
          <AlertCircle className="h-3 w-3" />
          {validation.error}
        </div>
      );
    }

    if (validation.warning) {
      return (
        <div className="flex items-center gap-1 text-sm text-amber-600 mt-1">
          <AlertTriangle className="h-3 w-3" />
          {validation.warning}
        </div>
      );
    }

    return null;
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Form-level warnings */}
      {formWarnings.length > 0 && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            <div className="space-y-1">
              {formWarnings.map((warning, index) => (
                <div key={index}>{warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      {/* Introduction */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Introduction
          </CardTitle>
          <CardDescription>
            Provide context and background for this activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Describe the context, purpose, and background of today's activities..."
            {...register('introduction')}
            rows={3}
          />
        </CardContent>
      </Card>

      {/* Activity Type */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Activity Information
          </CardTitle>
          <CardDescription>
            Basic information about today's activities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="activity_type">Activity Type *</Label>
            <Select
              value={activityType}
              onValueChange={(value: FieldActivityType) => setValue('activity_type', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select activity type..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="round_table_session">Round Table Session</SelectItem>
                <SelectItem value="school_visit">School Visit</SelectItem>
                <SelectItem value="meeting">Meeting</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Basic Activity Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="venue_location">Venue/Location</Label>
              <Input
                id="venue_location"
                placeholder="Specific venue within the school..."
                defaultValue={currentSchoolName}
                {...register('venue_location')}
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">
                {currentSchoolName ?
                  `Auto-populated from your check-in at ${currentSchoolName}` :
                  'Venue location will be auto-populated from your check-in'
                }
              </p>
            </div>
            <div>
              <Label htmlFor="activity_dates">Activity Date(s)</Label>
              <Input
                id="activity_dates"
                placeholder="Date or date range..."
                {...register('activity_dates')}
              />
            </div>
          </div>

          {activityType === 'round_table_session' && (
            <>
              {/* Round Tables */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Round Tables</Label>

                {/* Education Level Selector */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Add Education Level
                  </Label>
                  <Select
                    onValueChange={(value) => addEducationLevel(value)}
                  >
                    <SelectTrigger className="w-full md:w-64">
                      <SelectValue placeholder="Select education level..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="primary" disabled={selectedEducationLevels.includes('primary')}>
                        Primary
                      </SelectItem>
                      <SelectItem value="s1" disabled={selectedEducationLevels.includes('s1')}>
                        Senior 1
                      </SelectItem>
                      <SelectItem value="s2" disabled={selectedEducationLevels.includes('s2')}>
                        Senior 2
                      </SelectItem>
                      <SelectItem value="s3" disabled={selectedEducationLevels.includes('s3')}>
                        Senior 3
                      </SelectItem>
                      <SelectItem value="s4" disabled={selectedEducationLevels.includes('s4')}>
                        Senior 4
                      </SelectItem>
                      <SelectItem value="other" disabled={selectedEducationLevels.includes('other')}>
                        Other Levels
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Education Levels */}
                {selectedEducationLevels.length > 0 && (
                  <div className="space-y-3">
                    {selectedEducationLevels.map((level) => (
                      <div key={level} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <Label className="text-sm font-medium capitalize">
                            {level === 'primary' ? 'Primary' :
                             level === 's1' ? 'Senior 1' :
                             level === 's2' ? 'Senior 2' :
                             level === 's3' ? 'Senior 3' :
                             level === 's4' ? 'Senior 4' : 'Other Levels'}
                          </Label>
                          <Input
                            type="number"
                            min="0"
                            max="20"
                            placeholder="Number of round tables"
                            {...register(`round_tables_${level}` as keyof FormData, {
                              min: 0,
                              max: 20,
                              valueAsNumber: true
                            })}
                            className="mt-1"
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeEducationLevel(level)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Round Tables Summary */}
                {totalRoundTables > 0 && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Total Round Tables:</strong> {totalRoundTables} |
                      <strong> Expected Students:</strong> {expectedStudents} (8 per table)
                    </p>
                  </div>
                )}
              </div>

              {/* Student Attendance */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Student Attendance</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="total_students_attended">Total Students Present *</Label>
                    <Input
                      id="total_students_attended"
                      type="number"
                      min="0"
                      {...register('total_students_attended', {
                        required: 'Total students is required',
                        min: { value: 0, message: 'Must be 0 or greater' }
                      })}
                    />
                    {errors.total_students_attended && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.total_students_attended.message}
                      </p>
                    )}
                    {studentsAbsent > 0 && (
                      <p className="text-sm text-gray-600 mt-1">
                        Students absent: {studentsAbsent}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="champions_count">Champion Teachers/Students</Label>
                    <Input
                      id="champions_count"
                      type="number"
                      min="0"
                      {...register('champions_count', { min: 0 })}
                    />
                  </div>
                </div>
              </div>

              {/* Gender Breakdown */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Participant Gender Breakdown</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="male_participants">Male Participants</Label>
                    <Input
                      id="male_participants"
                      type="number"
                      min="0"
                      {...register('male_participants', { min: 0 })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="female_participants">Female Participants</Label>
                    <Input
                      id="female_participants"
                      type="number"
                      min="0"
                      {...register('female_participants', { min: 0 })}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Activities Conducted */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Activities Conducted
          </CardTitle>
          <CardDescription>
            List the specific activities you conducted today
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Add an activity..."
              value={newActivity}
              onChange={(e) => setNewActivity(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addActivity())}
            />
            <Button type="button" onClick={addActivity} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {activities.map((activity, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {activity}
                <button
                  type="button"
                  onClick={() => removeActivity(index)}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lesson/Topics Covered */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Lesson/Topics Covered
          </CardTitle>
          <CardDescription>
            Select the specific iLead lesson topics covered during the session
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Lesson Category Selector */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-2 block">
                Select Lesson Category
              </Label>
              <Select
                value={selectedLessonCategory}
                onValueChange={(value: 'iChoose' | 'iLead' | 'iDo') => setSelectedLessonCategory(value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose lesson category..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="iChoose">iChoose</SelectItem>
                  <SelectItem value="iLead">iLead</SelectItem>
                  <SelectItem value="iDo">iDo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Specific Lesson Selector */}
            {selectedLessonCategory && (
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  Select {selectedLessonCategory} Lesson
                </Label>
                <Select
                  onValueChange={(value) => {
                    const lesson = ALL_LESSON_TOPICS.find(l => l.id === value);
                    if (lesson && !topics.includes(lesson.title)) {
                      setTopics(prev => [...prev, lesson.title]);
                    }
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={`Select ${selectedLessonCategory} lesson...`} />
                  </SelectTrigger>
                  <SelectContent>
                    {getLessonsByCategory(selectedLessonCategory).map((lesson) => (
                      <SelectItem
                        key={lesson.id}
                        value={lesson.id}
                        disabled={topics.includes(lesson.title)}
                      >
                        {lesson.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Custom Topic Input */}
          <div className="border-t pt-4">
            <Label className="text-sm font-medium text-gray-700 mb-2 block">Custom Topics</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Add a custom topic..."
                value={newTopic}
                onChange={(e) => setNewTopic(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTopic())}
              />
              <Button type="button" onClick={addTopic} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Selected Topics Display */}
          {topics.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Selected Topics:</Label>
              <div className="flex flex-wrap gap-2">
                {topics.map((topic, index) => (
                  <Badge key={index} variant="outline" className="flex items-center gap-1">
                    {topic}
                    <button
                      type="button"
                      onClick={() => removeTopic(index)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Challenges, Wins, and Observations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Challenges Encountered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Describe any challenges or difficulties you faced..."
              {...register('challenges_encountered')}
              rows={4}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-green-500" />
              Wins & Successes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Describe successes, achievements, or positive outcomes..."
              {...register('wins_achieved')}
              rows={4}
            />
          </CardContent>
        </Card>
      </div>

      {/* Lessons Learned */}
      <Card>
        <CardHeader>
          <CardTitle>Lessons Learned</CardTitle>
          <CardDescription>
            What did you learn from today's activities?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="What did you learn from today's activities?"
            {...register('lessons_learned')}
            rows={4}
          />
        </CardContent>
      </Card>

      {/* Follow-up Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Follow-up Actions</CardTitle>
          <CardDescription>
            Indicate if any follow-up actions are required
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="follow_up_required"
              checked={followUpRequired}
              onCheckedChange={(checked) => setValue('follow_up_required', !!checked)}
            />
            <Label htmlFor="follow_up_required">
              Follow-up actions are required
            </Label>
          </div>

          {followUpRequired && (
            <Textarea
              placeholder="Describe the follow-up actions needed..."
              {...register('follow_up_actions')}
              rows={3}
            />
          )}
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommendations</CardTitle>
          <CardDescription>
            Based on your observations, what recommendations do you have?
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Provide specific recommendations based on your observations and findings..."
            {...register('recommendations')}
            rows={4}
          />
        </CardContent>
      </Card>

      {/* Photo Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Photo Documentation
          </CardTitle>
          <CardDescription>
            Upload photos to document field activities and observations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OptimizedPhotoUpload
            onPhotosChanged={handlePhotosChanged}
            maxFiles={5}
            compressionOptions={{
              maxWidth: 1920,
              maxHeight: 1080,
              quality: 0.8,
              format: 'jpeg',
              maxSizeKB: 500
            }}
            disabled={isSubmitting}
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full"
        size="lg"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Submitting Report...
          </>
        ) : (
          'Submit Field Report'
        )}
      </Button>
    </form>
  );
};

export default FieldReportForm;
